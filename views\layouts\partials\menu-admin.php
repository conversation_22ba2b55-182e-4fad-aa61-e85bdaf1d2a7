<?php
use yii\helpers\Url;
?>

<!-- Меню для администратора -->
<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','processes') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/tracking/index']); ?>" class="nav-link text-primary">
                <?php echo Yii::t('app','processes') ?>
            </a>
        </li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/debt/index']); ?>" class="nav-link text-primary">
                <?php echo Yii::t('app','debts') ?>
            </a>
        </li>
    </ul>
</li>

<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','clients') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/client/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','client_section') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/client-special-prices/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','special_prices') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/client-balance-adjustment/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','client_balance_adjustment') ?></a></li>
    </ul>
</li>



<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','salesSection') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/sales/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','sales') ?></a></li>
    </ul>
</li>

<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','finance') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/currency/view']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','currency') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/cashbox/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','cashbox') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/payment/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','pays') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/expenses/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','expenses') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/expenses-type/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','expense_type') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/cashier/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','cashier') ?></a></li>
    </ul>
</li>

<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','supplier') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/supplier/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','supplier') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/supplier-finance/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','supplier_material_income') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/supplier-finance/payments']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','supplier_finance') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/material-return/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','material_return') ?></a></li>
    </ul>
</li>

<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','storage') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/product/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','product') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/material-income/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','material_income') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/material/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','material') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/equipment/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','equipment') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/equipment-part/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','equipment_parts') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/material-category/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','material_categories') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/material-defect/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','defect') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/product-ingredients/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','ingredients') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/inventory-adjustment/index']); ?>" class="nav-link text-primary">Корректировка остатков</a></li>
    </ul>
</li>

<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','invoice') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/invoice/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','invoice') ?></a></li>
    </ul>
</li>

<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','settings') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/user/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','users') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/region/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','region') ?></a></li>
    </ul>
</li>

<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','workers') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/worker/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','workers') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/position/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','position') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/archive-worker/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','archived_workers') ?></a></li>
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/worker-payment/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','worker_payment_report') ?></a></li>
    </ul>
</li>

<li class="nav-item dropdown">
    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
        <span><?php echo Yii::t('app','reports') ?></span>
    </a>
    <ul class="dropdown-menu">
        <li class="nav-item"><a href="<?php echo Url::to(['/backend/report/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','all_reports') ?></a></li>
    </ul>
</li> 